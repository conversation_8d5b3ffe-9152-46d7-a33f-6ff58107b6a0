#pragma once

#include <string>
#include <memory>
#include <vector>

#if defined(_WIN32)
#include <windows.h>
#else
#include <dlfcn.h>
#endif

#define NOBASSMIDIOVERLOADS
#include "bass.h"
#include "bassmidi.h"
#include "bass_fx.h"
#include "alsa_midi.h"

// Forward declaration
class PianoKeyboard;

class AudioEngine {
public:
    AudioEngine();
    ~AudioEngine();

    // Initialize the audio engine
    bool Initialize();
    
    // Cleanup the audio engine
    void Cleanup();
    
    // Load a soundfont file (.sf2)
    bool LoadSoundfont(const std::string& soundfont_path);
    
    // Play a MIDI note
    void PlayNote(int note, int velocity = 127);

    // Stop a MIDI note
    void StopNote(int note);

    // Stop all notes
    void StopAllNotes();

    // Process audio for all engines
    void ProcessAudio();
    
    // Set master volume (0.0 to 1.0)
    void SetVolume(float volume);
    
    // Get current volume
    float GetVolume() const;
    
    // Check if audio engine is initialized
    bool IsInitialized() const;

    // Check if soundfont is loaded
    bool IsSoundfontLoaded() const;

    // Get current soundfont path
    const std::string& GetCurrentSoundfontPath() const;

    // Get polyphony information
    int GetCurrentPolyphony() const;
    int GetMaxPolyphony() const;

    // Set maximum polyphony (number of simultaneous voices)
    bool SetMaxPolyphony(int max_polyphony);

    // Test functions
    void PlayTestTone();
    void PrintDeviceInfo();

    // Debug information functions
    float GetCPUUsage() const;
    float GetRenderingTime() const;
    int GetActiveChannels() const;
    std::string GetAudioInfo() const;

    // MIDI input functions (BASS-based)
    bool InitializeMIDIInput();
    void CleanupMIDIInput();
    std::vector<std::string> GetMIDIInputDevices() const;
    bool OpenMIDIInputDevice(int device_id);
    void CloseMIDIInputDevice();
    bool IsMIDIInputOpen() const;
    std::string GetCurrentMIDIInputDevice() const;

    // ALSA MIDI input functions
    bool InitializeALSAMIDI();
    void CleanupALSAMIDI();
    std::vector<ALSAMIDIDevice> GetALSAMIDIDevices() const;
    bool OpenALSAMIDIDevice(int client_id, int port_id);
    void CloseALSAMIDIDevice();
    bool IsALSAMIDIOpen() const;
    ALSAMIDIDevice GetCurrentALSAMIDIDevice() const;

    // Piano keyboard integration for visual feedback
    void SetPianoKeyboard(PianoKeyboard* piano_keyboard);
    void NotifyMIDIKeyPressed(int note, bool pressed);





private:
    bool initialized_;
    HSTREAM midi_stream_;
    HSOUNDFONT soundfont_;
    float master_volume_;
    std::string current_soundfont_path_;

    // Polyphony tracking
    int max_polyphony_;
    mutable int current_polyphony_;

    // MIDI input (BASS-based)
    HRECORD midi_input_;
    int current_midi_input_device_;
    std::string current_midi_input_name_;

    // ALSA MIDI input
    std::unique_ptr<ALSAMIDIInput> alsa_midi_;

    // Piano keyboard for visual feedback
    PianoKeyboard* piano_keyboard_;





    // Dynamic library handles
    void* bass_lib_;
    void* bassmidi_lib_;
    void* bass_fx_lib_;

    // Function pointers using official BASS function signatures
    decltype(&BASS_Init) BASS_Init_ptr;
    decltype(&BASS_Free) BASS_Free_ptr;
    decltype(&BASS_MIDI_StreamCreate) BASS_MIDI_StreamCreate_ptr;
    decltype(&BASS_ChannelPlay) BASS_ChannelPlay_ptr;
    decltype(&BASS_ChannelSetAttribute) BASS_ChannelSetAttribute_ptr;
    decltype(&BASS_StreamFree) BASS_StreamFree_ptr;
    decltype(&BASS_MIDI_StreamEvent) BASS_MIDI_StreamEvent_ptr;
    decltype(&BASS_MIDI_FontInit) BASS_MIDI_FontInit_ptr;
    decltype(&BASS_MIDI_FontFree) BASS_MIDI_FontFree_ptr;
    decltype(&BASS_MIDI_StreamSetFonts) BASS_MIDI_StreamSetFonts_ptr;
    decltype(&BASS_ErrorGetCode) BASS_ErrorGetCode_ptr;
    decltype(&BASS_ChannelGetAttribute) BASS_ChannelGetAttribute_ptr;
    decltype(&BASS_ChannelIsActive) BASS_ChannelIsActive_ptr;
    decltype(&BASS_GetDeviceInfo) BASS_GetDeviceInfo_ptr;
    decltype(&BASS_SetConfig) BASS_SetConfig_ptr;
    decltype(&BASS_GetCPU) BASS_GetCPU_ptr;
    decltype(&BASS_GetInfo) BASS_GetInfo_ptr;
    decltype(&BASS_RecordGetDeviceInfo) BASS_RecordGetDeviceInfo_ptr;
    decltype(&BASS_RecordInit) BASS_RecordInit_ptr;
    decltype(&BASS_RecordFree) BASS_RecordFree_ptr;
    decltype(&BASS_RecordStart) BASS_RecordStart_ptr;
    decltype(&BASS_ChannelStop) BASS_ChannelStop_ptr;

    // Helper functions
    bool LoadBASSLibraries();
    void UnloadBASSLibraries();
    bool InitializeBASS();
    bool InitializeMIDI();
    void CleanupBASS();
    void CleanupMIDI();

    // Error handling
    std::string GetLastBASSError() const;
    void LogBASSError(const std::string& operation) const;
};
